{"per_channel": true, "reduce_range": true, "per_model_config": {"model": {"op_types": ["Constant", "<PERSON><PERSON><PERSON><PERSON>", "Reshape", "Sub", "Abs", "Mat<PERSON>ul", "Sqrt", "Slice", "Where", "Cast", "Div", "Concat", "<PERSON><PERSON><PERSON>", "Softmax", "Equal", "E<PERSON>", "Transpose", "ReduceSum", "Unsqueeze", "<PERSON><PERSON>", "Exp", "Conv", "<PERSON><PERSON>", "Add", "<PERSON>w", "Expand", "<PERSON><PERSON><PERSON><PERSON>", "ConstantOfShape"], "weight_type": "QUInt8"}}}