# 完成必要的import（下文省略）
from typing import Union, List

import onnxruntime
from PIL import Image
import numpy as np
# import torch
# import argparse
# import cn_clip.clip as clip
# from cn_clip.clip import load_from_name, available_models

def _convert_to_rgb(image):
    return image.convert('RGB')

def image_transform(image_size=224):
    def transform(image):
        # 转换为RGB
        image = _convert_to_rgb(image)
        # 调整大小
        image = image.resize((image_size, image_size), Image.BICUBIC)
        # 转换为numpy数组，使用float32类型
        image = np.array(image, dtype=np.float32) / 255.0
        # 标准化，使用float32类型的数组
        mean = np.array([0.48145466, 0.4578275, 0.40821073], dtype=np.float32)
        std = np.array([0.26862954, 0.26130258, 0.27577711], dtype=np.float32)
        image = (image - mean) / std
        # 调整维度顺序从 (H,W,C) 到 (C,H,W)
        image = np.transpose(image, (2, 0, 1))
        # 添加batch维度
        image = np.expand_dims(image, axis=0)
        # 确保输出是float32类型
        return image.astype(np.float32)

    return transform

_MODEL_INFO = {
    "ViT-B-16": {
        "struct": "ViT-B-16@RoBERTa-wwm-ext-base-chinese",
        "input_resolution": 224
    },
    "ViT-L-14": {
        "struct": "ViT-L-14@RoBERTa-wwm-ext-base-chinese",
        "input_resolution": 224
    },
    "ViT-L-14-336": {
        "struct": "ViT-L-14-336@RoBERTa-wwm-ext-base-chinese",
        "input_resolution": 336
    },
    "ViT-H-14": {
        "struct": "ViT-H-14@RoBERTa-wwm-ext-large-chinese",
        "input_resolution": 224
    },
    "RN50": {
        "struct": "RN50@RBT3-chinese",
        "input_resolution": 224
    },
}


from bert_tokenizer import FullTokenizer

_tokenizer = FullTokenizer()

def tokenize(texts: Union[str, List[str]], context_length: int = 52) -> np.ndarray:
    """
    Returns the tokenized representation of given input string(s)
    Parameters
    ----------
    texts : Union[str, List[str]]
        An input string or a list of input strings to tokenize
    context_length : int
        The context length to use; all baseline models use 52 as the context length
    Returns
    -------
    A two-dimensional numpy array containing the resulting tokens, shape = [number of input strings, context_length]
    """
    if isinstance(texts, str):
        texts = [texts]

    all_tokens = []
    for text in texts:
        all_tokens.append([_tokenizer.vocab['[CLS]']] + _tokenizer.convert_tokens_to_ids(_tokenizer.tokenize(text))[
                                                     :context_length - 2] + [_tokenizer.vocab['[SEP]']])

    result = np.zeros((len(all_tokens), context_length), dtype=np.int64)

    for i, tokens in enumerate(all_tokens):
        assert len(tokens) <= context_length
        result[i, :len(tokens)] = tokens

    return result


# 载入ONNX图像侧模型（**请替换${DATAPATH}为实际的路径**）
img_sess_options = onnxruntime.SessionOptions()
# 设置日志级别为 2 (ERROR)，忽略 WARNING
img_sess_options.log_severity_level = 2
# 禁用优化器
# img_sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_DISABLE_ALL

img_run_options = onnxruntime.RunOptions()
img_run_options.log_severity_level = 2
img_onnx_model_path="/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.img.fp16.onnx"
img_session = onnxruntime.InferenceSession(img_onnx_model_path,
                                        sess_options=img_sess_options,
                                     #    providers=["CUDAExecutionProvider"]
                                        )

# 预处理图片
model_arch = "ViT-B-16" # 这里我们使用的是ViT-B-16规模，其他规模请对应修改
preprocess = image_transform(_MODEL_INFO[model_arch]['input_resolution'])
# 示例皮卡丘图片，预处理后得到[1, 3, 分辨率, 分辨率]尺寸的numpy数组
image = preprocess(Image.open("/Users/<USER>/work/hanzi/py/img/pokemon.jpeg"))

#** 用ONNX模型计算图像侧特征
image_features = img_session.run(["unnorm_image_features"], {"image": image})[0] # 未归一化的图像特征
# L2 归一化
image_features = image_features / np.linalg.norm(image_features, axis=-1, keepdims=True) # 归一化后的Chinese-CLIP图像特征，用于下游任务
print(image_features.shape) # Numpy array shape: [1, 特征向量维度]


# 载入ONNX文本侧模型（**请替换${DATAPATH}为实际的路径**）
txt_sess_options = onnxruntime.SessionOptions()
txt_run_options = onnxruntime.RunOptions()
txt_run_options.log_severity_level = 2
txt_onnx_model_path="/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.txt.fp16.onnx"
txt_session = onnxruntime.InferenceSession(txt_onnx_model_path,
                                           sess_options=txt_sess_options,
                                           # providers=["CUDAExecutionProvider"]
                                           )




# 文本

# 为4条输入文本进行分词。序列长度指定为52，需要和转换ONNX模型时保持一致（参见转换时的context-length参数）
texts_list = ["皮卡丘","杰尼龟", "妙蛙种子", "小火龙"]
text = tokenize(texts_list, context_length=52)
print("Debug: Tokenized text shape:", text.shape)
print("Debug: Tokenized text:")
for i, txt in enumerate(texts_list):
    print(f"  {txt}: {text[i][:10]}...")  # Print first 10 tokens

#** 用ONNX模型依次计算文本侧特征
text_features = []
for i in range(len(text)):
    one_text = np.expand_dims(text[i], axis=0)
    text_feature = txt_session.run(["unnorm_text_features"], {"text": one_text})[0]  # 未归一化的文本特征
    text_features.append(text_feature)

# 将所有特征向量堆叠在一起
text_features = np.concatenate(text_features, axis=0)  # 4个特征向量stack到一起
# 对特征进行L2归一化
text_features = text_features / np.linalg.norm(text_features, axis=1, keepdims=True)  # 归一化后的Chinese-CLIP文本特征，用于下游任务
print(text_features.shape)  # Numpy array shape: [4, 特征向量维度]

# 内积后softmax
# 注意在内积计算时，由于对比学习训练时有temperature的概念
# 需要乘上模型logit_scale.exp()，我们的预训练模型logit_scale均为4.6052，所以这里乘以100
# 对于用户自己的ckpt，请使用torch.load载入后，查看ckpt['state_dict']['module.logit_scale']或ckpt['state_dict']['logit_scale']
logits_per_image = 100 * image_features @ text_features.T

# 计算softmax
def softmax(x):
    exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))  # 减去最大值以提高数值稳定性
    return exp_x / np.sum(exp_x, axis=-1, keepdims=True)


# 图文相似概率: [[1.2252e-03, 5.2874e-02, 6.7116e-04, 9.4523e-01]]
print(softmax(logits_per_image))  # 图文相似概率

# (1, 512)
# (4, 512)
# [[9.4365489e-01 1.2677772e-03 5.4394968e-02 6.8242900e-04]]