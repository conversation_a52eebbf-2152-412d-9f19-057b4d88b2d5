package main

import (
	"fmt"
	"log"

	"clip-go/features"
	"clip-go/image"
	"clip-go/inference"
	"clip-go/tokenizer"
)

func main() {
	// Initialize tokenizer
	vocabPath := "vocab.txt"
	tok, err := tokenizer.NewFullTokenizer(vocabPath, true)
	if err != nil {
		log.Fatalf("Failed to create tokenizer: %v", err)
	}

	// Model configuration
	modelArch := "ViT-B-16"
	modelInfo := image.ModelInfoMap[modelArch]

	// Image and text model paths (update these paths as needed)
	imgModelPath := "/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.img.fp16.onnx"
	txtModelPath := "/Users/<USER>/work/hanzi/py/model/clip/vit-b-16.txt.fp16.onnx"
	imagePath := "/Users/<USER>/work/hanzi/py/img/pokemon.jpeg"

	// Initialize ONNX sessions
	imgSession, err := inference.NewImageSession(imgModelPath)
	if err != nil {
		log.Fatalf("Failed to create image session: %v", err)
	}
	defer imgSession.Destroy()

	txtSession, err := inference.NewTextSession(txtModelPath)
	if err != nil {
		log.Fatalf("Failed to create text session: %v", err)
	}
	defer txtSession.Destroy()

	// Process image
	fmt.Println("Processing image...")
	imageData, err := image.ImageTransform(imagePath, modelInfo.InputResolution)
	if err != nil {
		log.Fatalf("Failed to transform image: %v", err)
	}

	// Run image inference
	imageShape := []int64{1, 3, int64(modelInfo.InputResolution), int64(modelInfo.InputResolution)}
	imageFeatures, err := imgSession.RunImageInference(imageData, imageShape)
	if err != nil {
		log.Fatalf("Failed to run image inference: %v", err)
	}

	fmt.Printf("Image features shape: [1, %d]\n", len(imageFeatures))

	// Process texts
	texts := []string{"皮卡丘", "杰尼龟", "妙蛙种子", "小火龙"}
	contextLength := 52

	fmt.Println("Processing texts...")
	var allTextFeatures [][]float32

	for _, text := range texts {
		// Tokenize text
		tokens := tok.Tokenize(text)
		tokenIDs := tok.ConvertTokensToIDs(tokens)

		// Debug: print tokenization results
		fmt.Printf("Debug: Text '%s' -> tokens: %v -> IDs: %v\n", text, tokens, tokenIDs)

		// Add CLS and SEP tokens, truncate if necessary
		clsID := tok.Vocab["[CLS]"]
		sepID := tok.Vocab["[SEP]"]

		// Build final token sequence
		finalTokens := []int32{clsID}
		maxContentLength := contextLength - 2 // Reserve space for CLS and SEP
		if len(tokenIDs) > maxContentLength {
			finalTokens = append(finalTokens, tokenIDs[:maxContentLength]...)
		} else {
			finalTokens = append(finalTokens, tokenIDs...)
		}
		finalTokens = append(finalTokens, sepID)

		// Debug: print final token sequence
		fmt.Printf("Debug: Final tokens for '%s': %v\n", text, finalTokens)

		// Pad to context length
		paddedTokens := make([]int64, contextLength)
		for i, token := range finalTokens {
			if i < contextLength {
				paddedTokens[i] = int64(token)
			}
		}

		// Run text inference
		textShape := []int64{1, int64(contextLength)}
		textFeature, err := txtSession.RunTextInference(paddedTokens, textShape)
		if err != nil {
			log.Fatalf("Failed to run text inference for '%s': %v", text, err)
		}

		allTextFeatures = append(allTextFeatures, textFeature)
	}

	fmt.Printf("Text features shape: [%d, %d]\n", len(allTextFeatures), len(allTextFeatures[0]))

	// Compute similarities
	fmt.Println("Computing similarities...")

	// The logit scale for the pretrained model is 4.6052, so exp(4.6052) ≈ 100
	logitScale := float32(100.0)

	similarities := features.ComputeSimilarityMatrix(imageFeatures, allTextFeatures, logitScale)

	// Print results
	fmt.Println("Image-text similarity probabilities:")
	for i, text := range texts {
		fmt.Printf("  %s: %.6f\n", text, similarities[i])
	}

	// Also print as array format like Python
	fmt.Print("[[")
	for i, sim := range similarities {
		if i > 0 {
			fmt.Print(" ")
		}
		fmt.Printf("%.8e", sim)
	}
	fmt.Println("]]")

	// Clean up ONNX Runtime environment
	defer inference.DestroyEnvironment()
}

// tokenizeText is a helper function that mimics the Python tokenize function
func tokenizeText(tok *tokenizer.FullTokenizer, texts []string, contextLength int) [][]int64 {
	result := make([][]int64, len(texts))

	for i, text := range texts {
		// Tokenize
		tokens := tok.Tokenize(text)
		tokenIDs := tok.ConvertTokensToIDs(tokens)

		// Add CLS and SEP tokens
		clsID := tok.Vocab["[CLS]"]
		sepID := tok.Vocab["[SEP]"]

		// Build sequence: [CLS] + tokens[:contextLength-2] + [SEP]
		sequence := []int64{int64(clsID)}
		maxContentLength := contextLength - 2

		for j, tokenID := range tokenIDs {
			if j >= maxContentLength {
				break
			}
			sequence = append(sequence, int64(tokenID))
		}
		sequence = append(sequence, int64(sepID))

		// Pad to context length
		paddedSequence := make([]int64, contextLength)
		copy(paddedSequence, sequence)

		result[i] = paddedSequence
	}

	return result
}
