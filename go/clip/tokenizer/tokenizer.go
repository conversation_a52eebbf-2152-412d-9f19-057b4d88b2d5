package tokenizer

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"unicode"

	"golang.org/x/text/unicode/norm"
)

// FullTokenizer implements the complete BERT tokenization pipeline
type FullTokenizer struct {
	Vocab              map[string]int32
	InvVocab           map[int32]string
	BasicTokenizer     *BasicTokenizer
	WordpieceTokenizer *WordpieceTokenizer
}

// BasicTokenizer handles basic tokenization (punctuation splitting, lower casing, etc.)
type BasicTokenizer struct {
	DoLowerCase bool
}

// WordpieceTokenizer handles WordPiece tokenization
type WordpieceTokenizer struct {
	Vocab                map[string]int32
	UnkToken             string
	MaxInputCharsPerWord int
}

// NewFullTokenizer creates a new FullTokenizer instance
func NewFullTokenizer(vocabFile string, doLowerCase bool) (*FullTokenizer, error) {
	// Load vocabulary
	vocab, err := loadVocab(vocabFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load vocab: %w", err)
	}

	// Create inverse vocabulary
	invVocab := make(map[int32]string, len(vocab))
	for k, v := range vocab {
		invVocab[v] = k
	}

	// Initialize tokenizers
	basicTokenizer := &BasicTokenizer{DoLowerCase: doLowerCase}
	wordpieceTokenizer := &WordpieceTokenizer{
		Vocab:                vocab,
		UnkToken:             "[UNK]",
		MaxInputCharsPerWord: 200,
	}

	return &FullTokenizer{
		Vocab:              vocab,
		InvVocab:           invVocab,
		BasicTokenizer:     basicTokenizer,
		WordpieceTokenizer: wordpieceTokenizer,
	}, nil
}

// DefaultVocab returns the default vocabulary file path
func DefaultVocab() string {
	// Get the directory of the current executable or working directory
	wd, err := os.Getwd()
	if err != nil {
		return "vocab.txt"
	}
	return filepath.Join(wd, "vocab.txt")
}

// loadVocab loads a vocabulary file into a map
func loadVocab(vocabFile string) (map[string]int32, error) {
	file, err := os.Open(vocabFile)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	vocab := make(map[string]int32)
	scanner := bufio.NewScanner(file)
	index := int32(0)

	for scanner.Scan() {
		token := strings.TrimSpace(scanner.Text())
		if token != "" {
			vocab[token] = index
			index++
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return vocab, nil
}

// Tokenize performs complete tokenization
func (ft *FullTokenizer) Tokenize(text string) []string {
	// Basic tokenization
	basicTokens := ft.BasicTokenizer.Tokenize(text)

	// WordPiece tokenization
	var wordpieceTokens []string
	for _, token := range basicTokens {
		subTokens := ft.WordpieceTokenizer.Tokenize(token)
		wordpieceTokens = append(wordpieceTokens, subTokens...)
	}

	return wordpieceTokens
}

// ConvertTokensToIDs converts tokens to their corresponding IDs
func (ft *FullTokenizer) ConvertTokensToIDs(tokens []string) []int32 {
	ids := make([]int32, len(tokens))
	for i, token := range tokens {
		if id, exists := ft.Vocab[token]; exists {
			ids[i] = id
		} else {
			ids[i] = ft.Vocab["[UNK]"]
		}
	}
	return ids
}

// ConvertIDsToTokens converts IDs to their corresponding tokens
func (ft *FullTokenizer) ConvertIDsToTokens(ids []int32) []string {
	tokens := make([]string, len(ids))
	for i, id := range ids {
		if token, exists := ft.InvVocab[id]; exists {
			tokens[i] = token
		} else {
			tokens[i] = "[UNK]"
		}
	}
	return tokens
}

// VocabSize returns the size of the vocabulary
func (ft *FullTokenizer) VocabSize() int {
	return len(ft.Vocab)
}

// convertToUnicode ensures the input is a valid Unicode string
func convertToUnicode(text string) string {
	return text // Go strings are already UTF-8
}

// whitespaceTokenize performs basic whitespace tokenization
func whitespaceTokenize(text string) []string {
	text = strings.TrimSpace(text)
	if text == "" {
		return []string{}
	}
	return strings.Fields(text)
}

// Tokenize performs basic tokenization
func (bt *BasicTokenizer) Tokenize(text string) []string {
	text = convertToUnicode(text)
	text = bt.cleanText(text)

	// Tokenize Chinese characters
	text = bt.tokenizeChineseChars(text)

	origTokens := whitespaceTokenize(text)
	var splitTokens []string

	for _, token := range origTokens {
		if bt.DoLowerCase {
			token = strings.ToLower(token)
			token = bt.runStripAccents(token)
		}
		splitTokens = append(splitTokens, bt.runSplitOnPunc(token)...)
	}

	outputTokens := whitespaceTokenize(strings.Join(splitTokens, " "))
	return outputTokens
}

// cleanText performs invalid character removal and whitespace cleanup
func (bt *BasicTokenizer) cleanText(text string) string {
	var output strings.Builder
	for _, char := range text {
		cp := int(char)
		if cp == 0 || cp == 0xfffd || isControl(char) {
			continue
		}
		if isWhitespace(char) {
			output.WriteRune(' ')
		} else {
			output.WriteRune(char)
		}
	}
	return output.String()
}

// runStripAccents strips accents from text
func (bt *BasicTokenizer) runStripAccents(text string) string {
	text = norm.NFD.String(text)
	var output strings.Builder
	for _, char := range text {
		if unicode.Is(unicode.Mn, char) {
			continue
		}
		output.WriteRune(char)
	}
	return output.String()
}

// runSplitOnPunc splits text on punctuation
func (bt *BasicTokenizer) runSplitOnPunc(text string) []string {
	chars := []rune(text)
	i := 0
	startNewWord := true
	var output [][]rune

	for i < len(chars) {
		char := chars[i]
		if isPunctuation(char) {
			output = append(output, []rune{char})
			startNewWord = true
		} else {
			if startNewWord {
				output = append(output, []rune{})
			}
			startNewWord = false
			output[len(output)-1] = append(output[len(output)-1], char)
		}
		i++
	}

	result := make([]string, len(output))
	for i, chars := range output {
		result[i] = string(chars)
	}
	return result
}

// tokenizeChineseChars adds whitespace around CJK characters
func (bt *BasicTokenizer) tokenizeChineseChars(text string) string {
	var output strings.Builder
	for _, char := range text {
		if bt.isChineseChar(int(char)) {
			output.WriteRune(' ')
			output.WriteRune(char)
			output.WriteRune(' ')
		} else {
			output.WriteRune(char)
		}
	}
	return output.String()
}

// isChineseChar checks if a codepoint is a CJK character
func (bt *BasicTokenizer) isChineseChar(cp int) bool {
	return (cp >= 0x4E00 && cp <= 0x9FFF) ||
		(cp >= 0x3400 && cp <= 0x4DBF) ||
		(cp >= 0x20000 && cp <= 0x2A6DF) ||
		(cp >= 0x2A700 && cp <= 0x2B73F) ||
		(cp >= 0x2B740 && cp <= 0x2B81F) ||
		(cp >= 0x2B820 && cp <= 0x2CEAF) ||
		(cp >= 0xF900 && cp <= 0xFAFF) ||
		(cp >= 0x2F800 && cp <= 0x2FA1F)
}

// Tokenize performs WordPiece tokenization
func (wt *WordpieceTokenizer) Tokenize(text string) []string {
	text = convertToUnicode(text)

	var outputTokens []string
	for _, token := range whitespaceTokenize(text) {
		chars := []rune(token)
		if len(chars) > wt.MaxInputCharsPerWord {
			outputTokens = append(outputTokens, wt.UnkToken)
			continue
		}

		isBad := false
		start := 0
		var subTokens []string

		for start < len(chars) {
			end := len(chars)
			var curSubstr string
			found := false

			for start < end {
				substr := string(chars[start:end])
				if start > 0 {
					substr = "##" + substr
				}
				if _, exists := wt.Vocab[substr]; exists {
					curSubstr = substr
					found = true
					break
				}
				end--
			}

			if !found {
				isBad = true
				break
			}
			subTokens = append(subTokens, curSubstr)
			start = end
		}

		if isBad {
			outputTokens = append(outputTokens, wt.UnkToken)
		} else {
			outputTokens = append(outputTokens, subTokens...)
		}
	}
	return outputTokens
}

// Helper functions for character classification

// isWhitespace checks if a character is whitespace
func isWhitespace(char rune) bool {
	if char == ' ' || char == '\t' || char == '\n' || char == '\r' {
		return true
	}
	return unicode.Is(unicode.Zs, char)
}

// isControl checks if a character is a control character
func isControl(char rune) bool {
	if char == '\t' || char == '\n' || char == '\r' {
		return false
	}
	return unicode.Is(unicode.Cc, char) || unicode.Is(unicode.Cf, char)
}

// isPunctuation checks if a character is punctuation
func isPunctuation(char rune) bool {
	cp := int(char)
	// ASCII punctuation
	if (cp >= 33 && cp <= 47) || (cp >= 58 && cp <= 64) ||
		(cp >= 91 && cp <= 96) || (cp >= 123 && cp <= 126) {
		return true
	}
	return unicode.Is(unicode.P, char)
}
