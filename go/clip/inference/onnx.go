package inference

import (
	"fmt"
	"runtime"

	ort "github.com/yalue/onnxruntime_go"
)

// ImageSession wraps ONNX Runtime session for image inference
type ImageSession struct {
	Session *ort.AdvancedSession
	Input   *ort.Tensor[float32]
	Output  *ort.Tensor[float32]
}

// TextSession wraps ONNX Runtime session for text inference
type TextSession struct {
	Session *ort.AdvancedSession
	Input   *ort.Tensor[int64]
	Output  *ort.Tensor[float32]
}

// getSharedLibPath returns the path to the ONNX Runtime shared library
func getSharedLibPath() string {
	if runtime.GOOS == "windows" {
		if runtime.GOARCH == "amd64" {
			return "../third_party/onnxruntime.dll"
		}
	}
	if runtime.GOOS == "darwin" {
		if runtime.GOARCH == "arm64" {
			return "../third_party/onnxruntime_arm64.dylib"
		}
		if runtime.GOARCH == "amd64" {
			return "../third_party/onnxruntime_arm64.dylib" // Use arm64 version for now
		}
	}
	if runtime.GOOS == "linux" {
		if runtime.GOARCH == "arm64" {
			return "../third_party/onnxruntime_arm64.so"
		}
		return "../third_party/onnxruntime.so"
	}
	panic("Unable to find a version of the onnxruntime library supporting this system.")
}

// NewImageSession creates a new image inference session
func NewImageSession(modelPath string) (*ImageSession, error) {
	ort.SetSharedLibraryPath(getSharedLibPath())
	err := ort.InitializeEnvironment()
	if err != nil {
		return nil, fmt.Errorf("Error initializing ORT environment: %w", err)
	}

	// Create input tensor for image: [1, 3, 224, 224] for ViT-B-16
	inputShape := ort.NewShape(1, 3, 224, 224)
	inputTensor, err := ort.NewEmptyTensor[float32](inputShape)
	if err != nil {
		return nil, fmt.Errorf("Error creating input tensor: %w", err)
	}

	// Create output tensor for image features: [1, 512] for ViT-B-16
	outputShape := ort.NewShape(1, 512)
	outputTensor, err := ort.NewEmptyTensor[float32](outputShape)
	if err != nil {
		inputTensor.Destroy()
		return nil, fmt.Errorf("Error creating output tensor: %w", err)
	}

	// Create session options
	options, err := ort.NewSessionOptions()
	if err != nil {
		inputTensor.Destroy()
		outputTensor.Destroy()
		return nil, fmt.Errorf("Error creating ORT session options: %w", err)
	}
	defer options.Destroy()

	// Create advanced session
	session, err := ort.NewAdvancedSession(modelPath,
		[]string{"image"}, []string{"unnorm_image_features"},
		[]ort.ArbitraryTensor{inputTensor},
		[]ort.ArbitraryTensor{outputTensor},
		options)
	if err != nil {
		inputTensor.Destroy()
		outputTensor.Destroy()
		return nil, fmt.Errorf("Error creating ORT session: %w", err)
	}

	return &ImageSession{
		Session: session,
		Input:   inputTensor,
		Output:  outputTensor,
	}, nil
}

// NewTextSession creates a new text inference session
func NewTextSession(modelPath string) (*TextSession, error) {
	ort.SetSharedLibraryPath(getSharedLibPath())
	err := ort.InitializeEnvironment()
	if err != nil {
		return nil, fmt.Errorf("Error initializing ORT environment: %w", err)
	}

	// Create input tensor for text: [1, 52] for context length 52
	inputShape := ort.NewShape(1, 52)
	inputTensor, err := ort.NewEmptyTensor[int64](inputShape)
	if err != nil {
		return nil, fmt.Errorf("Error creating input tensor: %w", err)
	}

	// Create output tensor for text features: [1, 512] for ViT-B-16
	outputShape := ort.NewShape(1, 512)
	outputTensor, err := ort.NewEmptyTensor[float32](outputShape)
	if err != nil {
		inputTensor.Destroy()
		return nil, fmt.Errorf("Error creating output tensor: %w", err)
	}

	// Create session options
	options, err := ort.NewSessionOptions()
	if err != nil {
		inputTensor.Destroy()
		outputTensor.Destroy()
		return nil, fmt.Errorf("Error creating ORT session options: %w", err)
	}
	defer options.Destroy()

	// Create advanced session
	session, err := ort.NewAdvancedSession(modelPath,
		[]string{"text"}, []string{"unnorm_text_features"},
		[]ort.ArbitraryTensor{inputTensor},
		[]ort.ArbitraryTensor{outputTensor},
		options)
	if err != nil {
		inputTensor.Destroy()
		outputTensor.Destroy()
		return nil, fmt.Errorf("Error creating ORT session: %w", err)
	}

	return &TextSession{
		Session: session,
		Input:   inputTensor,
		Output:  outputTensor,
	}, nil
}

// RunImageInference runs inference on image data
func (is *ImageSession) RunImageInference(imageData []float32, shape []int64) ([]float32, error) {
	// Copy image data to input tensor
	inputData := is.Input.GetData()
	if len(inputData) != len(imageData) {
		return nil, fmt.Errorf("Input tensor size mismatch: expected %d, got %d", len(inputData), len(imageData))
	}
	copy(inputData, imageData)

	// Run inference
	err := is.Session.Run()
	if err != nil {
		return nil, fmt.Errorf("Error running inference: %w", err)
	}

	// Get output data
	outputData := is.Output.GetData()
	result := make([]float32, len(outputData))
	copy(result, outputData)

	return result, nil
}

// RunTextInference runs inference on text data
func (ts *TextSession) RunTextInference(textData []int64, shape []int64) ([]float32, error) {
	// Copy text data to input tensor
	inputData := ts.Input.GetData()
	if len(inputData) != len(textData) {
		return nil, fmt.Errorf("Input tensor size mismatch: expected %d, got %d", len(inputData), len(textData))
	}
	copy(inputData, textData)

	// Run inference
	err := ts.Session.Run()
	if err != nil {
		return nil, fmt.Errorf("Error running inference: %w", err)
	}

	// Get output data
	outputData := ts.Output.GetData()
	result := make([]float32, len(outputData))
	copy(result, outputData)

	return result, nil
}

// Destroy cleans up the image session
func (is *ImageSession) Destroy() {
	if is.Session != nil {
		is.Session.Destroy()
	}
	if is.Input != nil {
		is.Input.Destroy()
	}
	if is.Output != nil {
		is.Output.Destroy()
	}
}

// Destroy cleans up the text session
func (ts *TextSession) Destroy() {
	if ts.Session != nil {
		ts.Session.Destroy()
	}
	if ts.Input != nil {
		ts.Input.Destroy()
	}
	if ts.Output != nil {
		ts.Output.Destroy()
	}
}

// DestroyEnvironment cleans up the ONNX Runtime environment
func DestroyEnvironment() {
	ort.DestroyEnvironment()
}
