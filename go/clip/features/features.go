package features

import (
	"fmt"
	"math"

	"gonum.org/v1/gonum/mat"
)

// L2NormalizeRows performs L2 normalization on each row of a matrix
func L2NormalizeRows(m *mat.Dense) {
	r, _ := m.Dims()
	for i := 0; i < r; i++ {
		rowVec := m.RowView(i)
		norm := mat.Norm(rowVec, 2)
		if norm > 0 {
			for j := 0; j < rowVec.Len(); j++ {
				m.Set(i, j, m.At(i, j)/norm)
			}
		}
	}
}

// L2NormalizeSlice performs L2 normalization on a slice
func L2NormalizeSlice(data []float32) []float32 {
	// Calculate L2 norm
	var norm float64
	for _, val := range data {
		norm += float64(val * val)
	}
	norm = math.Sqrt(norm)

	if norm == 0 {
		return data
	}

	// Normalize
	result := make([]float32, len(data))
	for i, val := range data {
		result[i] = float32(float64(val) / norm)
	}
	return result
}

// Softmax computes the softmax function
func Softmax(m *mat.Dense) *mat.Dense {
	r, c := m.Dims()
	result := mat.NewDense(r, c, nil)

	for i := 0; i < r; i++ {
		row := m.RawRowView(i)

		// Find maximum value for numerical stability
		maxVal := row[0]
		for j := 1; j < c; j++ {
			if row[j] > maxVal {
				maxVal = row[j]
			}
		}

		// Calculate exp values and sum
		var sumExp float64
		expVals := make([]float64, c)
		for j, val := range row {
			expVal := math.Exp(val - maxVal)
			expVals[j] = expVal
			sumExp += expVal
		}

		// Set normalized values
		for j, expVal := range expVals {
			result.Set(i, j, expVal/sumExp)
		}
	}
	return result
}

// SoftmaxSlice computes softmax on a slice
func SoftmaxSlice(data []float32) []float32 {
	if len(data) == 0 {
		return data
	}

	// Find maximum for numerical stability
	maxVal := data[0]
	for _, val := range data[1:] {
		if val > maxVal {
			maxVal = val
		}
	}

	// Calculate exp values and sum
	result := make([]float32, len(data))
	var sumExp float64
	for i, val := range data {
		expVal := math.Exp(float64(val - maxVal))
		result[i] = float32(expVal)
		sumExp += expVal
	}

	// Normalize
	for i := range result {
		result[i] = float32(float64(result[i]) / sumExp)
	}

	return result
}

// Float32ToFloat64 converts []float32 to []float64
func Float32ToFloat64(slice []float32) []float64 {
	result := make([]float64, len(slice))
	for i, v := range slice {
		result[i] = float64(v)
	}
	return result
}

// Float64ToFloat32 converts []float64 to []float32
func Float64ToFloat32(slice []float64) []float32 {
	result := make([]float32, len(slice))
	for i, v := range slice {
		result[i] = float32(v)
	}
	return result
}

// CreateMatrix creates a gonum matrix from a slice with given dimensions
func CreateMatrix(data []float32, rows, cols int) *mat.Dense {
	float64Data := Float32ToFloat64(data)
	return mat.NewDense(rows, cols, float64Data)
}

// MatrixToSlice converts a gonum matrix to a slice
func MatrixToSlice(m *mat.Dense) []float32 {
	r, c := m.Dims()
	result := make([]float32, r*c)
	for i := 0; i < r; i++ {
		for j := 0; j < c; j++ {
			result[i*c+j] = float32(m.At(i, j))
		}
	}
	return result
}

// ComputeSimilarity computes similarity between image and text features
func ComputeSimilarity(imageFeatures []float32, textFeatures [][]float32, logitScale float32) []float32 {
	// Normalize image features
	normalizedImageFeatures := L2NormalizeSlice(imageFeatures)

	// Normalize text features
	normalizedTextFeatures := make([][]float32, len(textFeatures))
	for i, textFeature := range textFeatures {
		normalizedTextFeatures[i] = L2NormalizeSlice(textFeature)
	}

	// Compute dot products (similarities)
	similarities := make([]float32, len(normalizedTextFeatures))
	for i, textFeature := range normalizedTextFeatures {
		var dotProduct float32
		for j := range normalizedImageFeatures {
			dotProduct += normalizedImageFeatures[j] * textFeature[j]
		}
		similarities[i] = dotProduct * logitScale
	}

	// Apply softmax
	return SoftmaxSlice(similarities)
}

// ComputeSimilarityMatrix computes similarity using matrix operations
func ComputeSimilarityMatrix(imageFeatures []float32, textFeatures [][]float32, logitScale float32) []float32 {
	// Create matrices
	imgMatrix := CreateMatrix(imageFeatures, 1, len(imageFeatures))

	// Stack text features
	textData := make([]float32, 0, len(textFeatures)*len(textFeatures[0]))
	for _, textFeature := range textFeatures {
		textData = append(textData, textFeature...)
	}
	txtMatrix := CreateMatrix(textData, len(textFeatures), len(textFeatures[0]))

	// L2 normalize
	L2NormalizeRows(imgMatrix)
	L2NormalizeRows(txtMatrix)

	// Debug: print first few values of normalized features
	fmt.Printf("Debug: Image features (first 5): [")
	for i := 0; i < 5 && i < len(imageFeatures); i++ {
		fmt.Printf("%.6f ", imgMatrix.At(0, i))
	}
	fmt.Printf("...]\n")

	fmt.Printf("Debug: Text features (first text, first 5): [")
	for i := 0; i < 5 && i < txtMatrix.RawMatrix().Cols; i++ {
		fmt.Printf("%.6f ", txtMatrix.At(0, i))
	}
	fmt.Printf("...]\n")

	// Compute logits = image_features @ text_features.T
	var logits mat.Dense
	logits.Mul(imgMatrix, txtMatrix.T())

	// Debug: print raw logits before scaling
	fmt.Printf("Debug: Raw logits: [")
	for i := 0; i < logits.RawMatrix().Cols; i++ {
		fmt.Printf("%.6f ", logits.At(0, i))
	}
	fmt.Printf("]\n")

	// Scale by logit_scale
	logits.Scale(float64(logitScale), &logits)

	// Debug: print scaled logits
	fmt.Printf("Debug: Scaled logits: [")
	for i := 0; i < logits.RawMatrix().Cols; i++ {
		fmt.Printf("%.6f ", logits.At(0, i))
	}
	fmt.Printf("]\n")

	// Apply softmax
	probs := Softmax(&logits)

	// Convert back to slice
	return MatrixToSlice(probs)
}
